package traintaskexecution

import (
	"context"

	"mlops/api/traintaskexecution/v1"
	"mlops/internal/service"
)

func (c *ControllerV1) ListTrainTaskExecution(ctx context.Context, req *v1.ListTrainTaskExecutionReq) (res *v1.ListTrainTaskExecutionRes, err error) {
	res = &v1.ListTrainTaskExecutionRes{}
	list, err := service.TrainTaskExecution().ListPage(ctx, req.TrainTaskExecutionListInput)
	if err != nil {
		return nil, err
	}
	res.CurrentPage = list.CurrentPage
	res.PageSize = list.PageSize
	res.Total = list.Total
	res.List = list.List
	return
}
