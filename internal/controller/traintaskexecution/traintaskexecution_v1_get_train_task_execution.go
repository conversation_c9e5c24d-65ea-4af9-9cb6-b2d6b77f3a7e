package traintaskexecution

import (
	"context"
	v1 "mlops/api/traintaskexecution/v1"

	"github.com/gogf/gf/v2/frame/g"
	"mlops/internal/dao"
	"mlops/internal/model/dto"
)

func (c *ControllerV1) GetTrainTaskExecution(ctx context.Context, req *v1.GetTrainTaskExecutionReq) (res *v1.GetTrainTaskExecutionRes, err error) {
	res = &v1.GetTrainTaskExecutionRes{}
	trainTaskExecution := &dto.TrainTaskExecution{}
	r := g.RequestFromCtx(ctx)
	err = dao.TrainTaskExecution.Ctx(ctx).Where(dao.TrainTaskExecution.Columns().Id, r.Get("id").Int()).Scan(trainTaskExecution)
	if err != nil {
		return nil, err
	}
	res.Data = trainTaskExecution
	return
}
