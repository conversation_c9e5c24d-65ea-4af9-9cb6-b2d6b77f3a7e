package team

import (
	"context"
	"mlops/internal/service"

	"mlops/api/team/v1"
)

func (c *ControllerV1) ListClusterNamespace(ctx context.Context, req *v1.ListClusterNamespaceReq) (res *v1.ListClusterNamespaceRes, err error) {
	clusterNamespaces, err := service.Team().ListRelatedClusterNamespace(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	res.ClusterNamespaces = clusterNamespaces

	return
}
