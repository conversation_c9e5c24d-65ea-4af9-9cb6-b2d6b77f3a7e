package dto

import "github.com/gogf/gf/v2/os/gtime"

type TrainTaskExecutionListInput struct {
	Page          int `v:"required"`
	PageSize      int `v:"required"`
	TaskId        uint
	TeamId        uint
	TaskName      string
	Statuses      []string
	TriggeredBy   string
	TriggerSource string
	StartTime     []*gtime.Time
	EndTime       []*gtime.Time
}

type TrainTaskExecutionListOutput struct {
	List        []*TrainTaskExecution `json:"list"`
	Total       int                   `json:"total"`
	CurrentPage int                   `json:"currentPage"`
	PageSize    int                   `json:"pageSize"`
}

type TrainTaskExecution struct {
	Id            uint        `json:"id"`
	TaskId        uint        `json:"taskId"`
	TaskName      string      `json:"taskName"`
	TeamId        uint        `json:"teamId"`
	TeamName      string      `json:"teamName"`
	ExecutionName string      `json:"executionName"`
	ClusterName   string      `json:"clusterName"`
	ClusterId     uint        `json:"clusterId"`
	Namespace     string      `json:"namespace"`
	ImageUrl      string      `json:"imageUrl"`
	StartTime     *gtime.Time `json:"startTime"`
	EndTime       *gtime.Time `json:"endTime"`
	Duration      uint        `json:"duration"`
	DashboardUrl  string      `json:"dashboardUrl"`
	Status        string      `json:"status"`
	TriggerSource string      `json:"triggerSource"`
	TriggeredBy   string      `json:"triggeredBy"`
	Priority      string      `json:"priority"`
}
