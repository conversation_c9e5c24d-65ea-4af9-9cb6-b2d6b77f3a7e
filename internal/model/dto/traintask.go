package dto

import "github.com/gogf/gf/v2/os/gtime"

type TrainTask struct {
	Id              uint                     `json:"id"`
	TaskName        string                   `json:"taskName"`
	TeamId          uint                     `json:"teamId"`
	TeamName        string                   `json:"teamName"`
	ClusterName     string                   `json:"clusterName"`
	ClusterId       uint                     `json:"clusterId"`
	Namespace       string                   `json:"namespace"`
	ImageUrl        string                   `json:"imageUrl"`
	StartCmd        string                   `json:"startCmd"`
	Priority        string                   `json:"priority"`
	TaskType        string                   `json:"taskType"`
	TaskYaml        string                   `json:"taskYaml"`
	LastStatus      string                   `json:"lastStatus"`
	CreatedBy       string                   `json:"createdBy"`
	UpdatedBy       string                   `json:"updatedBy"`
	CreatedAt       *gtime.Time              `json:"createdAt"`
	UpdatedAt       *gtime.Time              `json:"updatedAt"`
	ClusterResource TrainTaskClusterResource `json:"clusterResource"`
	VolumeMounts    []*TrainTaskVolumeMount  `json:"volumeMounts"`
}

type TrainTaskClusterResource struct {
	TaskId           uint   `json:"taskId"`
	MinReplicas      uint   `json:"minReplicas"`
	MaxReplicas      uint   `json:"maxReplicas"`
	RequestCpu       string `json:"requestCpu"`
	RequestMemory    string `json:"requestMemory"`
	RequestGpuCore   string `json:"requestGpuCore"`
	RequestGpuMemory string `json:"requestGpuMemory"`
	LimitCpu         string `json:"limitCpu"`
	LimitMemory      string `json:"limitMemory"`
	LimitGpuCore     string `json:"limitGpuCore"`
	LimitGpuMemory   string `json:"limitGpuMemory"`
}

type TrainTaskVolumeMount struct {
	Name       string `json:"name"`
	TaskId     uint   `json:"taskId"`
	VolumeName string `json:"volumeName"`
	MountPath  string `json:"mountPath"`
	SubPath    string `json:"subPath"`
	VolumeType string `json:"volumeType"`
}
