// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"mlops/internal/model/dto"
)

type (
	ITeam interface {
		Table() string
		Sync(ctx context.Context) (err error)
		// SyncTeamOrganization 根据用户id同步组织架构团队
		SyncTeamOrganization(ctx context.Context, userId int) (pid int64, err error)
		ListRelatedClusterNamespace(ctx context.Context, teamId int) (res []*dto.ClusterNamespace, err error)
	}
)

var (
	localTeam ITeam
)

func Team() ITeam {
	if localTeam == nil {
		panic("implement not found for interface ITeam, forgot register?")
	}
	return localTeam
}

func RegisterTeam(i ITeam) {
	localTeam = i
}
