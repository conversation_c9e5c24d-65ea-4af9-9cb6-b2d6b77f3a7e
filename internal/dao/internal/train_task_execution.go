// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// TrainTaskExecutionDao is the data access object for the table tt_train_task_execution.
type TrainTaskExecutionDao struct {
	table    string                    // table is the underlying table name of the DAO.
	group    string                    // group is the database configuration group name of the current DAO.
	columns  TrainTaskExecutionColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler        // handlers for customized model modification.
}

// TrainTaskExecutionColumns defines and stores column names for the table tt_train_task_execution.
type TrainTaskExecutionColumns struct {
	Id            string // 主键
	TaskId        string // 关联训练任务ID（外键）
	TeamId        string // 团队ID
	TeamName      string // 组名
	ExecutionName string // rayjob name
	ClusterName   string // 集群名称
	ClusterId     string // 集群ID
	Namespace     string // 命名空间
	ImageUrl      string // 镜像地址
	StartTime     string // 任务开始时间
	EndTime       string // 任务结束时间（NULL表示未结束）
	Duration      string // 持续时长（分钟）
	DashboardUrl  string // 监控面板URL
	Status        string // 任务状态
	TriggerSource string // 触发来源
	TriggeredBy   string // 触发者标识（用户ID或系统）
	Priority      string // 优先级
	CreatedAt     string // 创建时间
	UpdatedAt     string // 更新时间
	DeletedAt     string // 软删除时间（NULL表示未删除）
}

// trainTaskExecutionColumns holds the columns for the table tt_train_task_execution.
var trainTaskExecutionColumns = TrainTaskExecutionColumns{
	Id:            "id",
	TaskId:        "task_id",
	TeamId:        "team_id",
	TeamName:      "team_name",
	ExecutionName: "execution_name",
	ClusterName:   "cluster_name",
	ClusterId:     "cluster_id",
	Namespace:     "namespace",
	ImageUrl:      "image_url",
	StartTime:     "start_time",
	EndTime:       "end_time",
	Duration:      "duration",
	DashboardUrl:  "dashboard_url",
	Status:        "status",
	TriggerSource: "trigger_source",
	TriggeredBy:   "triggered_by",
	Priority:      "priority",
	CreatedAt:     "created_at",
	UpdatedAt:     "updated_at",
	DeletedAt:     "deleted_at",
}

// NewTrainTaskExecutionDao creates and returns a new DAO object for table data access.
func NewTrainTaskExecutionDao(handlers ...gdb.ModelHandler) *TrainTaskExecutionDao {
	return &TrainTaskExecutionDao{
		group:    "default",
		table:    "tt_train_task_execution",
		columns:  trainTaskExecutionColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *TrainTaskExecutionDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *TrainTaskExecutionDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *TrainTaskExecutionDao) Columns() TrainTaskExecutionColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *TrainTaskExecutionDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *TrainTaskExecutionDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *TrainTaskExecutionDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
