package traintask

import (
	"context"
	"fmt"
	"mlops/internal/dao"
	"mlops/internal/model/dto"
	"mlops/internal/model/entity"
	"mlops/internal/service"

	"github.com/gogf/gf/util/guid"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/os/gtime"
)

const (
	VolumeMountName = "vol-%d"
	ExecutionName   = "train-task-%d-%s"
)

type sTrainTask struct {
}

func init() {
	service.RegisterTrainTask(newsTrainTask())
}

func newsTrainTask() *sTrainTask {
	return &sTrainTask{}
}

func (this *sTrainTask) Table() string {
	return dao.TrainTask.Table()
}

func (this *sTrainTask) Get(ctx context.Context, id uint) (trainTask *dto.TrainTask, err error) {
	trainTask = &dto.TrainTask{}
	err = dao.TrainTask.Ctx(ctx).Where(dao.TrainTask.Columns().Id, id).Scan(trainTask)
	if err != nil {
		return nil, err
	}
	trainTask.ClusterResource = dto.TrainTaskClusterResource{}
	trainTask.VolumeMounts = []*dto.TrainTaskVolumeMount{}
	err = dao.TrainTaskClusterResource.Ctx(ctx).Where(dao.TrainTaskClusterResource.Columns().TaskId, id).Scan(&trainTask.ClusterResource)
	if err != nil {
		return nil, err
	}
	err = dao.TrainTaskVolumeMount.Ctx(ctx).Where(dao.TrainTaskVolumeMount.Columns().TaskId, id).Scan(&trainTask.VolumeMounts)
	if err != nil {
		return nil, err
	}
	return
}

func (this *sTrainTask) Create(ctx context.Context, trainTask *dto.TrainTask) (err error) {
	trainTaskEntity := toTrainTaskEntity(trainTask)
	dao.TrainTask.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		id, err := tx.Model(dao.TrainTask.Table()).Data(trainTaskEntity).InsertAndGetId()
		if err != nil {
			return err
		}
		trainTask.ClusterResource.TaskId = uint(id)
		_, err = tx.Model(dao.TrainTaskClusterResource.Table()).Where(dao.TrainTaskClusterResource.Columns().TaskId, trainTask.Id).Data(trainTask.ClusterResource).Insert()
		if err != nil {
			return err
		}
		for i, volumeMount := range trainTask.VolumeMounts {
			volumeMount.TaskId = uint(id)
			volumeMount.Name = fmt.Sprintf(VolumeMountName, i)
		}
		_, err = tx.Model(dao.TrainTaskVolumeMount.Table()).Where(dao.TrainTaskVolumeMount.Columns().TaskId, trainTask.Id).Data(trainTask.VolumeMounts).Insert()
		if err != nil {
			return err
		}
		return nil
	})
	return
}

func (this *sTrainTask) Update(ctx context.Context, trainTask *dto.TrainTask) (err error) {
	trainTaskEntity := toTrainTaskEntity(trainTask)
	dao.TrainTask.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err = tx.Model(dao.TrainTask.Table()).Where(dao.TrainTask.Columns().Id, trainTask.Id).Data(trainTaskEntity).FieldsEx(dao.TrainTask.Columns().CreatedAt, dao.TrainTask.Columns().CreatedBy).Update()
		if err != nil {
			return err
		}
		_, err = tx.Model(dao.TrainTaskClusterResource.Table()).Where(dao.TrainTaskClusterResource.Columns().TaskId, trainTask.Id).Delete()
		if err != nil {
			return err
		}
		_, err = tx.Model(dao.TrainTaskVolumeMount.Table()).Where(dao.TrainTaskVolumeMount.Columns().TaskId, trainTask.Id).Delete()
		if err != nil {
			return err
		}
		trainTask.ClusterResource.TaskId = trainTask.Id
		_, err = tx.Model(dao.TrainTaskClusterResource.Table()).Where(dao.TrainTaskClusterResource.Columns().TaskId, trainTask.Id).Data(trainTask.ClusterResource).Insert()
		if err != nil {
			return err
		}
		for i, volumeMount := range trainTask.VolumeMounts {
			volumeMount.TaskId = trainTask.Id
			volumeMount.Name = fmt.Sprintf(VolumeMountName, i)
		}
		_, err = tx.Model(dao.TrainTaskVolumeMount.Table()).Where(dao.TrainTaskVolumeMount.Columns().TaskId, trainTask.Id).Data(trainTask.VolumeMounts).Insert()
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return
}

func (this *sTrainTask) Delete(ctx context.Context, id uint) (err error) {
	err = dao.TrainTask.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err = tx.Model(dao.TrainTask.Table()).Where(dao.TrainTask.Columns().Id, id).Delete()
		if err != nil {
			return err
		}
		_, err = tx.Model(dao.TrainTaskExecution.Table()).Where(dao.TrainTaskExecution.Columns().TaskId, id).Delete()
		if err != nil {
			return err
		}
		_, err = tx.Model(dao.TrainTaskClusterResource.Table()).Where(dao.TrainTaskClusterResource.Columns().TaskId, id).Delete()
		if err != nil {
			return err
		}
		_, err = tx.Model(dao.TrainTaskVolumeMount.Table()).Where(dao.TrainTaskVolumeMount.Columns().TaskId, id).Delete()
		if err != nil {
			return err
		}
		return nil
	})
	return
}

func (this *sTrainTask) Trigger(ctx context.Context, id uint, triggerSource string, triggeredBy string) (err error) {
	trainTask := &entity.TrainTask{}
	err = dao.TrainTask.Ctx(ctx).Where(dao.TrainTask.Columns().Id, id).Scan(trainTask)
	if err != nil {
		return err
	}
	return trigger(ctx, trainTask, triggerSource, triggeredBy)
}

func trigger(ctx context.Context, trainTask *entity.TrainTask, triggerSource string, triggeredBy string) (err error) {
	trainTaskExecution := toTrainTaskExecution(trainTask)
	trainTaskExecution.TriggerSource = triggerSource
	trainTaskExecution.TriggeredBy = triggeredBy
	_, err = dao.TrainTaskExecution.Ctx(ctx).Data(trainTaskExecution).Insert()
	if err != nil {
		return err
	}
	// TODO add trigger logic
	// rayjob := &v1.RayJob{}
	// client.CreateRayjob(ctx, trainTask.ClusterName, rayjob)
	return
}

func toTrainTaskExecution(trainTask *entity.TrainTask) *entity.TrainTaskExecution {
	return &entity.TrainTaskExecution{
		TaskId:        trainTask.Id,
		TeamId:        trainTask.TeamId,
		TeamName:      trainTask.TeamName,
		ExecutionName: fmt.Sprintf(ExecutionName, trainTask.Id, guid.S()),
		ClusterName:   trainTask.ClusterName,
		ClusterId:     trainTask.ClusterId,
		Namespace:     trainTask.Namespace,
		ImageUrl:      trainTask.ImageUrl,
		Priority:      trainTask.Priority,
		StartTime:     gtime.Now(),
		Status:        "PENDING",
	}
}

func toTrainTaskEntity(trainTask *dto.TrainTask) *entity.TrainTask {
	return &entity.TrainTask{
		Id:          trainTask.Id,
		TaskName:    trainTask.TaskName,
		TeamId:      trainTask.TeamId,
		TeamName:    trainTask.TeamName,
		ClusterName: trainTask.ClusterName,
		ClusterId:   trainTask.ClusterId,
		Namespace:   trainTask.Namespace,
		ImageUrl:    trainTask.ImageUrl,
		StartCmd:    trainTask.StartCmd,
		Priority:    trainTask.Priority,
		TaskType:    trainTask.TaskType,
		TaskYaml:    trainTask.TaskYaml,
		CreatedBy:   trainTask.CreatedBy,
		UpdatedBy:   trainTask.UpdatedBy,
	}
}
