package traintaskexecution

import (
	"context"
	"mlops/internal/dao"
	"mlops/internal/model/dto"
	"mlops/internal/service"
)

type sTrainTaskExecution struct {
}

func init() {
	service.RegisterTrainTaskExecution(newsTrainTaskExecution())
}

func newsTrainTaskExecution() *sTrainTaskExecution {
	return &sTrainTaskExecution{}
}

func (this *sTrainTaskExecution) Table() string {
	return dao.TrainTaskExecution.Table()
}

func (this *sTrainTaskExecution) ListPage(ctx context.Context, in dto.TrainTaskExecutionListInput) (pageData *dto.TrainTaskExecutionListOutput, err error) {
	list := make([]*dto.TrainTaskExecution, 0)
	q := dao.TrainTaskExecution.Ctx(ctx)
	if in.TaskId != 0 {
		q = q.Where(dao.TrainTaskExecution.Columns().TaskId, in.TaskId)
	}
	if in.TaskName != "" {
		q = q.WhereLike(dao.TrainTaskExecution.Columns().ExecutionName, "%"+in.TaskName+"%")
	}
	if len(in.Statuses) > 0 {
		q = q.WhereIn(dao.TrainTaskExecution.Columns().Status, in.Statuses)
	}
	if in.TriggeredBy != "" {
		q = q.Where(dao.TrainTaskExecution.Columns().TriggeredBy, in.TriggeredBy)
	}
	if in.TriggerSource != "" {
		q = q.Where(dao.TrainTaskExecution.Columns().TriggerSource, in.TriggerSource)
	}
	if len(in.StartTime) > 0 {
		q = q.WhereBetween(dao.TrainTaskExecution.Columns().StartTime, in.StartTime[0], in.StartTime[1])
	}
	if len(in.EndTime) > 0 {
		q = q.WhereBetween(dao.TrainTaskExecution.Columns().EndTime, in.EndTime[0], in.EndTime[1])
	}
	q = q.LeftJoin(dao.TrainTask.Table(), "tt_train_task_execution.task_id = tt_train_task.id")
	total, err := q.Count()
	if err != nil {
		return nil, err
	}
	err = q.Fields("tt_train_task_execution.*", "tt_train_task.task_name").Page(in.Page, in.PageSize).Scan(&list)
	if err != nil {
		return nil, err
	}

	pageData = &dto.TrainTaskExecutionListOutput{
		List:        list,
		Total:       total,
		CurrentPage: in.Page,
		PageSize:    in.PageSize,
	}
	return
}

func (this *sTrainTaskExecution) List(ctx context.Context, in dto.TrainTaskExecutionListInput) (list []*dto.TrainTaskExecution, err error) {
	list = make([]*dto.TrainTaskExecution, 0)
	q := dao.TrainTaskExecution.Ctx(ctx)
	if in.TaskId != 0 {
		q = q.Where(dao.TrainTaskExecution.Columns().TaskId, in.TaskId)
	}
	if in.TaskName != "" {
		q = q.WhereLike(dao.TrainTaskExecution.Columns().ExecutionName, "%"+in.TaskName+"%")
	}
	if len(in.Statuses) > 0 {
		q = q.WhereIn(dao.TrainTaskExecution.Columns().Status, in.Statuses)
	}
	if in.TriggeredBy != "" {
		q = q.Where(dao.TrainTaskExecution.Columns().TriggeredBy, in.TriggeredBy)
	}
	if in.TriggerSource != "" {
		q = q.Where(dao.TrainTaskExecution.Columns().TriggerSource, in.TriggerSource)
	}
	if in.StartTime != nil {
		q = q.WhereGTE(dao.TrainTaskExecution.Columns().StartTime, in.StartTime)
	}
	if in.EndTime != nil {
		q = q.WhereLTE(dao.TrainTaskExecution.Columns().EndTime, in.EndTime)
	}
	err = q.Scan(&list)
	if err != nil {
		return nil, err
	}
	return
}

func (this *sTrainTaskExecution) Get(ctx context.Context, id uint) (trainTaskExecution *dto.TrainTaskExecution, err error) {
	trainTaskExecution = &dto.TrainTaskExecution{}
	err = dao.TrainTaskExecution.Ctx(ctx).Where(dao.TrainTaskExecution.Columns().Id, id).Scan(trainTaskExecution)
	if err != nil {
		return nil, err
	}
	return
}

func (this *sTrainTaskExecution) Interrupt(ctx context.Context, id uint) (err error) {
	trainTaskExecution := &dto.TrainTaskExecution{}
	err = dao.TrainTaskExecution.Ctx(ctx).Where(dao.TrainTaskExecution.Columns().Id, id).Scan(trainTaskExecution)
	if err != nil {
		return err
	}
	// TODO add interrupt logic
	return
}
