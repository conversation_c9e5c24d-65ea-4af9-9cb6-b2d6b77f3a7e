package v1

import (
	"mlops/internal/model/dto"

	"github.com/gogf/gf/v2/frame/g"
)

type ListTrainTaskExecutionReq struct {
	g.Meta `path:"/traintaskexecution/list" method:"get" tags:"TrainTaskExecution" summary:"list train task execution"`
	dto.TrainTaskExecutionListInput
}

type ListTrainTaskExecutionRes struct {
	dto.TrainTaskExecutionListOutput
}

type GetTrainTaskExecutionReq struct {
	g.Meta `path:"/traintaskexecution/:id" method:"get" tags:"TrainTaskExecution" summary:"get train task execution"`
}

type GetTrainTaskExecutionRes struct {
	Data interface{} `json:"data"`
}

type InterruptTrainTaskExecutionReq struct {
	g.Meta `path:"/traintaskexecution/interrupt" method:"post" tags:"TrainTaskExecution" summary:"interrupt train task execution"`
	Id     uint `v:"required#id is required"`
}

type InterruptTrainTaskExecutionRes struct {
}
