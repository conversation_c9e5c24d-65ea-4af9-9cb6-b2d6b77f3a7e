// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package team

import (
	"context"

	"mlops/api/team/v1"
)

type ITeamV1 interface {
	ListClusterNamespace(ctx context.Context, req *v1.ListClusterNamespaceReq) (res *v1.ListClusterNamespaceRes, err error)
}
