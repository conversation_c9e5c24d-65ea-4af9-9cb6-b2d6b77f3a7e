package v1

import (
	"mlops/internal/model/dto"
	"mlops/tools/common"

	"github.com/gogf/gf/v2/frame/g"
)

type ListTrainTaskReq struct {
	g.Meta `path:"/traintask/list" method:"get" tags:"TrainTask" sm:"list train task"`
	common.ListReq
}

type ListTrainTaskRes struct {
	*common.ListRes
}

type GetTrainTaskReq struct {
	g.Meta `path:"/traintask/:id" method:"get" tags:"TrainTask" sm:"get train task"`
	Id     uint `v:"required#id is required"`
}

type GetTrainTaskRes struct {
	Data interface{} `json:"data"`
}

type CreateTrainTaskReq struct {
	g.Meta          `path:"/traintask/create" method:"post" tags:"TrainTask" sm:"create train task"`
	TaskName        string                       `json:"taskName"`
	TeamId          uint                         `json:"teamId"`
	TeamName        string                       `json:"teamName"`
	ClusterName     string                       `json:"clusterName"`
	ClusterId       uint                         `json:"clusterId"`
	Namespace       string                       `json:"namespace"`
	ImageUrl        string                       `json:"imageUrl"`
	StartCmd        string                       `json:"startCmd"`
	Priority        string                       `json:"priority"`
	TaskType        string                       `json:"taskType"`
	TaskYaml        string                       `json:"taskYaml"`
	CreatedBy       string                       `json:"createdBy"`
	UpdatedBy       string                       `json:"updatedBy"`
	ClusterResource dto.TrainTaskClusterResource `json:"clusterResource"`
	VolumeMounts    []*dto.TrainTaskVolumeMount  `json:"volumeMounts"`
}

type CreateTrainTaskRes struct {
}

type UpdateTrainTaskReq struct {
	g.Meta          `path:"/traintask/update/:id" method:"patch" tags:"TrainTask" sm:"update train task"`
	Id              uint                         `v:"required#id is required"`
	TaskName        string                       `json:"taskName"`
	ClusterName     string                       `json:"clusterName"`
	ClusterId       uint                         `json:"clusterId"`
	Namespace       string                       `json:"namespace"`
	ImageUrl        string                       `json:"imageUrl"`
	StartCmd        string                       `json:"startCmd"`
	Priority        string                       `json:"priority"`
	TaskType        string                       `json:"taskType"`
	TaskYaml        string                       `json:"taskYaml"`
	CreatedBy       string                       `json:"createdBy"`
	UpdatedBy       string                       `json:"updatedBy"`
	ClusterResource dto.TrainTaskClusterResource `json:"clusterResource"`
	VolumeMounts    []*dto.TrainTaskVolumeMount  `json:"volumeMounts"`
}

type UpdateTrainTaskRes struct {
}

type DeleteTrainTaskReq struct {
	g.Meta `path:"/traintask/delete/:id" method:"delete" tags:"TrainTask" sm:"delete train task"`
	Id     uint `v:"required#id is required"`
}

type DeleteTrainTaskRes struct {
}

type TriggerTrainTaskReq struct {
	g.Meta        `path:"/traintask/trigger/:id" method:"post" tags:"TrainTask" sm:"trigger train task"`
	Id            uint   `v:"required#id is required"`
	TriggerSource string `json:"triggerSource"`
	TriggeredBy   string `json:"triggeredBy"`
}

type TriggerTrainTaskRes struct {
}

/**
1. 获取当前集群空间下来的 configmap列表
2. 获取当前集群空间下来的 secret列表
3. 获取当前集群空间下来的 pvc列表
*/

type ListConfigmapWithClusterNamespaceReq struct {
	g.Meta      `path:"/traintask/configmap/list" method:"get" tags:"TrainTask" sm:"list traintask configmaps"`
	ClusterName string `json:"clusterName"`
	Namespace   string `json:"namespace"`
}

type ListConfigmapWithClusterNamespaceRes struct {
	ConfigmapNames []string `json:"configmapNames"`
}

type ListSecretWithClusterNamespaceReq struct {
	g.Meta      `path:"/traintask/secret/list" method:"get" tags:"TrainTask" sm:"list traintask secrets"`
	ClusterName string `json:"clusterName"`
	Namespace   string `json:"namespace"`
}

type ListSecretWithClusterNamespaceRes struct {
	SecretNames []string `json:"secretNames"`
}

type ListPvcWithClusterNamespaceReq struct {
	g.Meta      `path:"/traintask/pvc/list" method:"get" tags:"TrainTask" sm:"list traintask pvcs"`
	ClusterName string `json:"clusterName"`
	Namespace   string `json:"namespace"`
}

type ListPvcWithClusterNamespaceRes struct {
	PvcNames []string `json:"pvcNames"`
}
